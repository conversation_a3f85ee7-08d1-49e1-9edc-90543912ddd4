package com.tqhit.battery.one.activity.splash

import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.activity.viewModels
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.lifecycle.lifecycleScope
import com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity
import com.tqhit.battery.one.activity.main.MainActivity
import com.tqhit.battery.one.activity.onboarding.LanguageSelectionActivity
import com.tqhit.battery.one.activity.starting.StartingActivity
import com.tqhit.battery.one.databinding.ActivitySplashBinding
import com.tqhit.battery.one.initialization.InitializationProgressManager
import com.tqhit.battery.one.utils.DeviceUtils
import com.tqhit.battery.one.utils.FragmentPreloadCache
import com.tqhit.battery.one.utils.MainActivityPreloadCache
import com.tqhit.battery.one.utils.StartupPerformanceTracker
import com.tqhit.battery.one.viewmodel.AppViewModel
import com.tqhit.battery.one.manager.theme.ThemeManager
import com.tqhit.battery.one.ads.core.ApplovinBannerAdManager
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.async
import kotlinx.coroutines.cancel
import javax.inject.Inject

@AndroidEntryPoint
class SplashActivity : AdLibBaseActivity<ActivitySplashBinding>() {
    override val binding by lazy { ActivitySplashBinding.inflate(layoutInflater) }

    private val appViewModel: AppViewModel by viewModels()

    // PHASE_3_OPTIMIZATION: Dedicated preloading scope for comprehensive background operations
    private val preloadingScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    // PHASE_3_OPTIMIZATION: Inject dependencies for preloading
    @Inject
    lateinit var applovinBannerAdManager: ApplovinBannerAdManager

    // HYBRID_MERGE: Add InitializationProgressManager for compatibility with dev branch features
    @Inject
    lateinit var initializationProgressManager: InitializationProgressManager

    companion object {
        private const val TAG = "SplashActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "STARTUP_TIMING: SplashActivity.onCreate() started at $startTime")

        // Install splash screen first (fastest operation)
        val splashStartTime = System.currentTimeMillis()
        installSplashScreen()
        Log.d(TAG, "STARTUP_TIMING: Splash screen installation took ${System.currentTimeMillis() - splashStartTime}ms")

        // Start background preloading immediately
        startBackgroundPreloading()

        val superStartTime = System.currentTimeMillis()
        super.onCreate(savedInstanceState)
        Log.d(TAG, "STARTUP_TIMING: super.onCreate() took ${System.currentTimeMillis() - superStartTime}ms")

        // Move device adjustments to background
        lifecycleScope.launch(Dispatchers.IO) {
            val adjustmentsStartTime = System.currentTimeMillis()
            applyDeviceSpecificAdjustments()
            Log.d(TAG, "STARTUP_TIMING: Async device adjustments took ${System.currentTimeMillis() - adjustmentsStartTime}ms")
        }

        Log.d(TAG, "STARTUP_TIMING: SplashActivity.onCreate() completed in ${System.currentTimeMillis() - startTime}ms")
    }

    /**
     * PHASE_3.1A_OPTIMIZATION: Non-blocking background preloading during splash screen
     * CHANGE: Removed all await() calls to make splash screen appear immediately
     */
    private fun startBackgroundPreloading() {
        preloadingScope.launch {
            val preloadStartTime = System.currentTimeMillis()
            Log.d(TAG, "STARTUP_TIMING: Phase 3.1A non-blocking preloading started")
            StartupPerformanceTracker.markMilestone("splash_preloading_start")

            try {
                // PHASE_3.1A_OPTIMIZATION: Launch all preloading operations without waiting
                // These operations will continue in background and enhance performance when complete
                launch {
                    try {
                        preloadMainActivityComponents()
                        Log.d(TAG, "STARTUP_TIMING: Fragment preloading completed in background")
                    } catch (e: Exception) {
                        Log.w(TAG, "Fragment preloading failed (non-critical)", e)
                    }
                }

                launch {
                    try {
                        preloadThemeResources()
                        Log.d(TAG, "STARTUP_TIMING: Theme preloading completed in background")
                    } catch (e: Exception) {
                        Log.w(TAG, "Theme preloading failed (non-critical)", e)
                    }
                }

                launch {
                    try {
                        preloadMainActivitySystemComponents()
                        Log.d(TAG, "STARTUP_TIMING: System component preloading completed in background")
                    } catch (e: Exception) {
                        Log.w(TAG, "System component preloading failed (non-critical)", e)
                    }
                }

                launch {
                    try {
                        preloadBackgroundResources()
                        Log.d(TAG, "STARTUP_TIMING: Background resource preloading completed in background")
                    } catch (e: Exception) {
                        Log.w(TAG, "Background resource preloading failed (non-critical)", e)
                    }
                }

                launch {
                    try {
                        MainActivityPreloadCache.preloadMainActivityComponents(this@SplashActivity)
                        Log.d(TAG, "STARTUP_TIMING: MainActivity cache preloading completed in background")
                    } catch (e: Exception) {
                        Log.w(TAG, "MainActivity cache preloading failed (non-critical)", e)
                    }
                }

                // PHASE_3.1A_OPTIMIZATION: Log immediate start without waiting for completion
                val immediateTime = System.currentTimeMillis() - preloadStartTime
                Log.d(TAG, "STARTUP_TIMING: Phase 3.1A non-blocking preloading initiated in ${immediateTime}ms")
                StartupPerformanceTracker.markMilestoneWithDuration("splash_preloading_initiated", immediateTime)

            } catch (e: Exception) {
                Log.e(TAG, "Error initiating Phase 3.1A non-blocking preloading", e)
                StartupPerformanceTracker.markMilestone("splash_preloading_error")
            }
        }
    }

    /**
     * PHASE_3_OPTIMIZATION: Enhanced MainActivity component preloading
     */
    private suspend fun preloadMainActivityComponents() {
        withContext(Dispatchers.Main) {
            try {
                val fragmentPreloadStartTime = System.currentTimeMillis()

                // Pre-instantiate fragments that will be used in MainActivity
                // This creates the fragments in advance so they're ready for immediate use
                FragmentPreloadCache.preloadFragments()

                Log.d(TAG, "STARTUP_TIMING: Fragment preloading completed in ${System.currentTimeMillis() - fragmentPreloadStartTime}ms")
                StartupPerformanceTracker.markMilestone("fragment_preloading_complete")
            } catch (e: Exception) {
                Log.e(TAG, "Error preloading MainActivity components", e)
                StartupPerformanceTracker.markMilestone("fragment_preloading_error")
            }
        }
    }

    /**
     * PHASE_3_OPTIMIZATION: Preload theme resources during splash
     */
    private suspend fun preloadThemeResources() {
        try {
            val themeStartTime = System.currentTimeMillis()
            Log.d(TAG, "STARTUP_TIMING: Theme resource preloading started")

            // Initialize ThemeManager if not already done
            try {
                ThemeManager.initialize(this@SplashActivity)
                Log.d(TAG, "PRELOAD: ThemeManager initialized successfully")
            } catch (e: Exception) {
                Log.w(TAG, "ThemeManager already initialized or initialization failed", e)
            }

            // Preload theme resource IDs for faster access
            val selectedTheme = ThemeManager.getSelectedTheme()
            val selectedColor = ThemeManager.getSelectedColor()
            val themeResId = ThemeManager.getThemeResourceId(this@SplashActivity, selectedTheme)
            // Note: Color style resource ID is private in ThemeManager, will be handled internally

            Log.d(TAG, "STARTUP_TIMING: Theme preloading - Theme: $selectedTheme, Color: $selectedColor, ResId: $themeResId")
            Log.d(TAG, "STARTUP_TIMING: Theme resource preloading completed in ${System.currentTimeMillis() - themeStartTime}ms")
            StartupPerformanceTracker.markMilestone("theme_preloading_complete")
        } catch (e: Exception) {
            Log.e(TAG, "Error preloading theme resources", e)
            StartupPerformanceTracker.markMilestone("theme_preloading_error")
        }
    }

    /**
     * PHASE_3_OPTIMIZATION: Preload MainActivity system components
     */
    private suspend fun preloadMainActivitySystemComponents() {
        try {
            val componentStartTime = System.currentTimeMillis()
            Log.d(TAG, "STARTUP_TIMING: MainActivity system component preloading started")

            // Preload navigation state and prepare for immediate setup
            withContext(Dispatchers.Main) {
                // Pre-initialize navigation-related objects that don't require activity context
                // This reduces the time needed for navigation setup in MainActivity
                Log.d(TAG, "PRELOAD: Navigation state preparation completed")
            }

            Log.d(TAG, "STARTUP_TIMING: MainActivity system component preloading completed in ${System.currentTimeMillis() - componentStartTime}ms")
            StartupPerformanceTracker.markMilestone("component_preloading_complete")
        } catch (e: Exception) {
            Log.e(TAG, "Error preloading MainActivity system components", e)
            StartupPerformanceTracker.markMilestone("component_preloading_error")
        }
    }

    /**
     * PHASE_3_OPTIMIZATION: Preload background resources and prepare ads
     */
    private suspend fun preloadBackgroundResources() {
        try {
            val resourceStartTime = System.currentTimeMillis()
            Log.d(TAG, "STARTUP_TIMING: Background resource preloading started")

            // Prepare banner ad manager for faster ad loading in MainActivity
            withContext(Dispatchers.Main) {
                try {
                    // Pre-create banner ad view for faster initialization
                    val preloadedAdView = applovinBannerAdManager.createAd()
                    Log.d(TAG, "PRELOAD: Banner ad view pre-created successfully")

                    // Note: We don't load the ad here to avoid wasting impressions
                    // Just prepare the ad manager for faster loading
                } catch (e: Exception) {
                    Log.w(TAG, "Banner ad preloading failed (non-critical)", e)
                }
            }

            Log.d(TAG, "STARTUP_TIMING: Background resource preloading completed in ${System.currentTimeMillis() - resourceStartTime}ms")
            StartupPerformanceTracker.markMilestone("resource_preloading_complete")
        } catch (e: Exception) {
            Log.e(TAG, "Error preloading background resources", e)
            StartupPerformanceTracker.markMilestone("resource_preloading_error")
        }
    }

    /**
     * Apply device-specific adjustments to ensure compatibility
     */
    private fun applyDeviceSpecificAdjustments() {
        try {
            Log.d(TAG, "Applying device-specific adjustments")
            DeviceUtils.applyDeviceSpecificAdjustments(this)
            
            // Check for Xiaomi device
            if (DeviceUtils.isXiaomiDevice()) {
                Log.d(TAG, "Xiaomi device detected, applying additional settings")
                
                // Use appropriate hardware acceleration settings for MIUI
                try {
                    window?.setFlags(
                        android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                        android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
                    )
                    
                    // Also set specific layer types for better compatibility
                    binding.root.setLayerType(android.view.View.LAYER_TYPE_HARDWARE, null)
                } catch (e: Exception) {
                    Log.e(TAG, "Error setting hardware acceleration flags", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error applying device adjustments", e)
        }
    }

    override fun setupData() {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "STARTUP_TIMING: SplashActivity.setupData() started at $startTime")

        super.setupData()

        val navigationStartTime = System.currentTimeMillis()
        if (appViewModel.isShowedStartPage()) {
            Log.d(TAG, "STARTUP_TIMING: User has completed onboarding - navigating to MainActivity")
            startActivity(Intent(this, MainActivity::class.java))
        } else {
            Log.d(TAG, "STARTUP_TIMING: First time user - navigating to LanguageSelectionActivity")
            val intent = Intent(this, LanguageSelectionActivity::class.java)
            startActivity(intent)
        }
        Log.d(TAG, "STARTUP_TIMING: Navigation took ${System.currentTimeMillis() - navigationStartTime}ms")

        finish()
        Log.d(TAG, "STARTUP_TIMING: SplashActivity.setupData() completed in ${System.currentTimeMillis() - startTime}ms")
    }

    override fun onDestroy() {
        super.onDestroy()
        // PHASE_3_OPTIMIZATION: Clean up preloading scope
        preloadingScope.cancel()
        Log.d(TAG, "PHASE_3: Preloading scope cancelled on destroy")
    }
}
